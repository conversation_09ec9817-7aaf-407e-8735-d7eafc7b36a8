// import api from '@/api'
import Vue from 'vue'

export const dictStore = {
  namespaced: true,
  state: {
    dictData: {},
    loading: false,
    error: null
  },
  mutations: {
    SET_DICT_DATA(state, { type, data }) {
      state.dictData[type] = data
    },
    SET_LOADING(state, status) {
      state.loading = status
    },
    SET_ERROR(state, error) {
      state.error = error
    }
  },
  actions: {
    async fetchDictData({ commit }) {
      commit('SET_LOADING', true)
      try {
        const response = await Vue.prototype.$request({
          url: '/custom/crm-common/dataDictionary/queryDataDictionaryValue',
          method: 'post',
          params: ['new_clue_strain', 'CRM_XSLY', 'strain', 'client_xs_type']
        })
        // console.log('fetchDictData字典请求', response.data)

        // 将扁平数组按dictionaryCode分组
        const groupedData = response.data.reduce((acc, item) => {
          const { dictionaryCode } = item
          if (!acc[dictionaryCode]) {
            acc[dictionaryCode] = []
          }
          // 转换为组件常用的 { value, label } 格式
          acc[dictionaryCode].push({
            value: item.valueCode,
            label: item.valueName,
            ...item // 保留原始字段
          })
          return acc
        }, {})

        Object.keys(groupedData).forEach((type) => {
          commit('SET_DICT_DATA', { type, data: groupedData[type] })
        })
        commit('SET_ERROR', null)
      } catch (error) {
        console.error(error)
        commit('SET_ERROR', error.message || '获取数据字典失败')
      } finally {
        commit('SET_LOADING', false)
      }
    }
  },
  getters: {
    getDictByType: (state) => (type) => {
      return state.dictData[type] || []
    },
    getDictLabel: (state) => (type, value) => {
      const dict = state.dictData[type] || []
      const item = dict.find((item) => item.value === value)
      return item ? item.label : value
    }
  }
}
