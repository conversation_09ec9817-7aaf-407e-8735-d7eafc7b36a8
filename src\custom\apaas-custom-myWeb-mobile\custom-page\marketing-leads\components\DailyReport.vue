<template>
  <div class="lead-progress">
    <!-- Stats Cards -->
    <div class="stats-cards">
      <div class="stat-card blue">
        <div class="stat-icon">
          今
        </div>
        <div class="stat-content">
          <div class="stat-label">
            今日
          </div>
          <div class="stat-value">
            {{ totalStats.today }}
          </div>
        </div>
      </div>
      <div class="stat-card purple">
        <div class="stat-icon">
          月
        </div>
        <div class="stat-content">
          <div class="stat-label">
            本月累计
          </div>
          <div class="stat-value">
            {{ totalStats.toMoon }}
          </div>
        </div>
      </div>
      <div class="stat-card orange">
        <div class="stat-icon">
          年
        </div>
        <div class="stat-content">
          <div class="stat-label">
            年累计
          </div>
          <div class="stat-value">
            {{ totalStats.toYear }}
          </div>
        </div>
      </div>
      <div class="stat-card light-blue">
        <div class="stat-icon">
          成
        </div>
        <div class="stat-content">
          <div class="stat-label">
            达成量
          </div>
          <div class="stat-value">
            {{ totalStats.achieve }}
          </div>
        </div>
      </div>
    </div>

    <!-- Data Table -->
    <div class="data-table">
      <div v-for="department in departmentData" :key="department.region" class="department-section">
        <div class="department-header" @click="toggleDepartment(department.region)">
          <div class="report-header">
            <h3 class="department-name">
              {{ formatDepartmentName(department.region) }}
            </h3>
          </div>
          <div class="header-row">
            <div class="cell">
              <div>今日</div>
              <div>{{ department.today }}</div>
            </div>
            <div class="cell">
              <div>本月累计</div>
              <div>{{ department.toMoon }}</div>
            </div>
            <div class="cell">
              <div>年累计</div>
              <div>{{ department.toYear }}</div>
            </div>
            <div class="cell">
              <div>达成量</div>
              <div>{{ department.achieve }}</div>
            </div>
          </div>
          <span class="expand-icon" :class="{ expanded: department.expanded }">▼</span>
        </div>
        <div v-show="department.expanded">
          <div v-for="brand in department.brandList" :key="brand.brand" class="data-row">
            <div class="cell type">
              {{ formatBrandName(brand.brand) }}
            </div>
            <div class="cell">
              {{ brand.today }}
            </div>
            <div class="cell">
              {{ brand.toMoon }}
            </div>
            <div class="cell">
              {{ brand.toYear }}
            </div>
            <div class="cell rate">
              {{ brand.achieve }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pull to Refresh -->
    <div v-if="refreshing" class="refresh-indicator">
      <div class="loading-spinner small"></div>
      <span>刷新中...</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LeadProgress',
  props: {
    dateRange: {
      type: Object,
      required: true
    },
    filters: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      departmentData: [],
      refreshing: false
    }
  },
  computed: {
    totalStats() {
      return this.departmentData.reduce(
        (acc, department) => {
          acc.today += Number(department.today) || 0
          acc.toMoon += Number(department.toMoon) || 0
          acc.toYear += Number(department.toYear) || 0
          acc.achieve += Number(department.achieve) || 0
          return acc
        },
        { today: 0, toMoon: 0, toYear: 0, achieve: 0 }
      )
    }
  },
  watch: {
    dateRange: {
      handler() {
        this.fetchData()
      },
      deep: true
    },
    filters: {
      handler() {
        this.fetchData()
      },
      deep: true
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    initData() {
      // 初始化数据
      this.departmentData = []
    },
    formatDepartmentName(name) {
      if (!name || name.length <= 2) return name
      // 仅在第一行第二个字符后换行
      return name.substring(0, 2) + '\n' + name.substring(2, name.length)
    },

    formatBrandName(brandStr) {
      if (!brandStr) return '未知品牌'
      try {
        // 解析品牌字符串，去掉方括号和引号
        const brands = JSON.parse(brandStr)
        if (Array.isArray(brands) && brands.length > 0) {
          return brands.join(', ')
        }
        return '未知品牌'
      } catch (e) {
        return brandStr || '未知品牌'
      }
    },
    async fetchData() {
      try {
        // this.$emit('loading', true)
        // 调用真实API
        const response = await this.mockData()
        console.log('获取线索进度数据成功：', response)

        // 为每个部门添加展开状态，默认第一个展开
        this.departmentData = response.map((department, index) => ({
          ...department,
          expanded: index === 0 // 第一个部门默认展开
        }))

        // this.$emit('data-updated', {
        //   totalStats: this.totalStats,
        //   departments: this.departmentData
        // })
      } catch (error) {
        console.error('错误获取：Failed to fetch lead progress data:', error)
      } finally {
        // this.$emit('loading', false)
      }
    },
    mockData() {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve([
            {
              region: '未知',
              today: 0,
              toMoon: 0,
              toYear: 2,
              achieve: 0,
              brandList: [
                {
                  brand: '["br002"]',
                  today: 0,
                  toMoon: 0,
                  toYear: 2,
                  achieve: 0
                }
              ]
            },
            {
              region: '临时组织',
              today: 0,
              toMoon: 0,
              toYear: 13,
              achieve: 0,
              brandList: [
                {
                  brand: '["br002"]',
                  today: 0,
                  toMoon: 0,
                  toYear: 7,
                  achieve: 0
                },
                {
                  brand: '["br015"]',
                  today: 0,
                  toMoon: 0,
                  toYear: 1,
                  achieve: 0
                },
                {
                  brand: '["br004"]',
                  today: 0,
                  toMoon: 0,
                  toYear: 1,
                  achieve: 0
                },
                {
                  brand: '["br144"]',
                  today: 0,
                  toMoon: 0,
                  toYear: 1,
                  achieve: 0
                },
                {
                  brand: '["br001"]',
                  today: 0,
                  toMoon: 0,
                  toYear: 1,
                  achieve: 0
                },
                {
                  brand: '["br005"]',
                  today: 0,
                  toMoon: 0,
                  toYear: 1,
                  achieve: 0
                },
                {
                  brand: '["br006"]',
                  today: 0,
                  toMoon: 0,
                  toYear: 1,
                  achieve: 0
                }
              ]
            },
            {
              region: '政企数字化事业部',
              today: 0,
              toMoon: 0,
              toYear: 1,
              achieve: 0,
              brandList: [
                {
                  brand: '[]',
                  today: 0,
                  toMoon: 0,
                  toYear: 1,
                  achieve: 0
                }
              ]
            },
            {
              region: '北京市场部',
              today: 0,
              toMoon: 0,
              toYear: 2,
              achieve: 0,
              brandList: [
                {
                  brand: '["br001"]',
                  today: 0,
                  toMoon: 0,
                  toYear: 1,
                  achieve: 0
                },
                {
                  brand: '["br003"]',
                  today: 0,
                  toMoon: 0,
                  toYear: 1,
                  achieve: 0
                }
              ]
            }
          ])
        })
      })
    },
    async fetchDepartmentData() {
      try {
        // 调用真实API接口
        const { data } = await this.$request({
          url: 'custom/clue/queryClueByDailyReport',
          method: 'post',
          data: {
            startDate: this.dateRange?.startDate,
            endDate: this.dateRange?.endDate,
            ...this.filters
          },
          headers: {
            'Content-Type': 'application/json'
          }
        })
        return data || []
      } catch (error) {
        console.error('API调用失败:', error)
        this.$emit('error', '获取日报数据失败，请重试')
        return []
      }
    },

    toggleDepartment(region) {
      const department = this.departmentData.find((d) => d.region === region)
      if (department) {
        department.expanded = !department.expanded
      }
    },

    async refreshData() {
      this.refreshing = true
      try {
        await this.fetchData()
      } finally {
        this.refreshing = false
      }
    }
  }
}
</script>

<style scoped>
.report-header {
  min-width: 66px;
  padding: 10px 10px;
  background: linear-gradient(90deg, #d1e4f9 0%, #e5f0fc 100%);
  font-weight: bold;
  line-height: 20px;
  font-size: 18px;
}

.department-name {
  word-break: break-word;
  white-space: pre-wrap;
}

.lead-progress {
  padding-bottom: 20px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 12px 8px;
  text-align: center;
  display: flex;
  justify-content: space-around;
}

.stat-content {
  display: flex;
  flex-direction: column;
}

.stat-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 4px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.stat-card.blue .stat-icon {
  background: #1890ff;
}

.stat-card.purple .stat-icon {
  background: #722ed1;
}

.stat-card.orange .stat-icon {
  background: #fa8c16;
}

.stat-card.light-blue .stat-icon {
  background: #13c2c2;
}

.stat-label {
  font-size: 10px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.data-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.department-section {
  border-bottom: 1px solid #f0f0f0;
}

.department-section:last-child {
  border-bottom: none;
}

.department-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s;
}

.department-header:hover {
  background: #f0f0f0;
}

.expand-icon {
  color: #999;
  font-size: 12px;
  transition: transform 0.2s;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.data-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  padding: 8px 16px;
  border-bottom: 1px solid #f5f5f5;
  font-size: 12px;
}

.data-row:last-child {
  border-bottom: none;
}

.header-row {
  background: #f8f9fa;
  font-weight: 500;
  color: #666;
  display: grid;
  font-size: 13px;
  grid-template-columns: repeat(4, 1fr);
}

.cell {
  padding: 4px;
  text-align: center;
  line-height: 20px;
}

.cell.type {
  text-align: left;
  color: #333;
}

.cell.rate {
  color: #1890ff;
}

.refresh-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  color: #666;
  font-size: 14px;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
