<template>
  <div>
    <van-field
      v-model="selectedLabel"
      :label="label"
      :placeholder="`请选择${label}`"
      readonly
      is-link
      @click="showPopup"
    />
    <van-popup v-model="visible" position="bottom">
      <van-picker show-toolbar :columns="columns" @confirm="onConfirm" @cancel="visible = false" />
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'YcPicker',
  props: {
    // 当前选中的值
    selectedValue: {
      type: [String, Number],
      default: ''
    },
    // 选项列表
    options: {
      type: Array,
      default: () => []
    },
    // 单元格标题
    label: {
      type: String,
      default: '请选择'
    }
  },
  data() {
    return {
      visible: false,
      selectedLabel: ''
    }
  },
  computed: {
    columns() {
      return this.options.map((option) => option.label)
    }
  },
  watch: {
    selectedValue(newVal) {
      console.log('newVal', newVal)
      // 强制清空逻辑：无论是否找到选项，只要modelValue为空则立即清空
      if (!newVal) {
        this.selectedLabel = ''
        return
      }

      // 保持原有选项匹配逻辑
      const selectedItem = this.options.find((item) => item.value === newVal)
      this.selectedLabel = selectedItem ? selectedItem.label : ''
    }
  },
  emits: ['update:modelValue', 'change'],
  mounted() {
    const selectedItem = this.options.find((item) => item.value === this.modelValue)
    if (selectedItem) {
      this.selectedLabel = selectedItem.label
    }
  },
  methods: {
    // 新增强制同步方法
    syncSelectedLabel() {
      if (!this.modelValue) {
        this.selectedLabel = ''
        return
      }

      const selectedItem = this.options.find((item) => item.value === this.modelValue)
      this.selectedLabel = selectedItem ? selectedItem.label : ''
    },
    showPopup() {
      this.visible = true
    },
    onConfirm(value, index) {
      const selectedItem = this.options[index]
      this.selectedLabel = selectedItem.label
      this.$emit('change', selectedItem.value)
      this.visible = false
    }
  }
}
</script>

<style scoped>
.selected-label {
  color: #333;
}
</style>
