<template>
  <div class="daily-report">
    <!-- News List -->
    <div class="news-list">
      <div v-if="filteredNews.length === 0" class="empty-state">
        <div class="empty-icon">
          📄
        </div>
        <p class="empty-text">
          暂无相关数据
        </p>
        <button class="refresh-btn" @click="refreshData">
          刷新数据
        </button>
      </div>

      <div v-else>
        <div
          v-for="news in paginatedNews"
          :key="news.id"
          class="news-item"
          @click="viewDetail(news)"
        >
          <div class="news-content">
            <div style="display: flex; justify-content: space-between;">
              <div style="display: flex;">
                <h4 class="news-title">
                  {{ news.theme }}
                </h4>
              </div>
              <div class="news-actions">
                <div class="detail-btn" @click.stop="viewDetail(news)">
                  抢单
                </div>
              </div>
            </div>
            <p class="news-description">
              {{ news?.new_progress }}
            </p>
            <div class="news-meta">
              <span class="news-meta-item author">📍 {{ news.region }}</span>
              <span class="news-meta-item type">🔢 {{ news.need_num || '-' }}</span>
              <span class="news-meta-item type">📅 {{ news.docking_date || '-' }}</span>
              <span class="news-meta-item type">🔄{{ news.duration || '-' }}天</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Load More -->
    <div v-if="hasMore && filteredNews.length > 0" class="load-more">
      <button class="load-more-btn" :disabled="loadingMore" @click="loadMore">
        {{ loadingMore ? '加载中...' : '加载更多' }}
      </button>
    </div>

    <!-- Pagination Info -->
    <div v-if="filteredNews.length > 0" class="pagination-info">
      显示 {{ Math.min(currentPage * pageSize, filteredNews.length) }} /
      {{ filteredNews.length }} 条记录
    </div>
  </div>
</template>

<script>
export default {
  name: 'DailyReport',
  props: {
    dateRange: {
      type: Object,
      required: true
    },
    filters: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      newsList: [],
      searchKeyword: '',
      currentFilter: 'all',
      currentPage: 1,
      pageSize: 10,
      loadingMore: false,
      filterTabs: [
        { value: 'all', label: '全部' },
        { value: 'important', label: '重要' },
        { value: 'urgent', label: '紧急' },
        { value: 'completed', label: '已完成' }
      ]
    }
  },
  computed: {
    filteredNews() {
      let filtered = this.newsList

      // 按关键词搜索
      if (this.searchKeyword.trim()) {
        const keyword = this.searchKeyword.toLowerCase()
        filtered = filtered.filter(
          (news) =>
            news.title.toLowerCase().includes(keyword) ||
            news.description.toLowerCase().includes(keyword) ||
            news.department.toLowerCase().includes(keyword)
        )
      }

      // 按类型筛选
      if (this.currentFilter !== 'all') {
        filtered = filtered.filter((news) => news.category === this.currentFilter)
      }

      return filtered
    },
    paginatedNews() {
      return this.filteredNews.slice(0, this.currentPage * this.pageSize)
    },
    hasMore() {
      return this.filteredNews.length > this.currentPage * this.pageSize
    }
  },
  watch: {
    dateRange: {
      handler() {
        this.resetPagination()
        this.fetchData()
      },
      deep: true
    },
    filters: {
      handler() {
        this.resetPagination()
        this.fetchData()
      },
      deep: true
    },
    currentFilter() {
      this.resetPagination()
    },
    searchKeyword() {
      this.resetPagination()
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    this.fetchData()
  },

  beforeDestroy() {
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  },
  methods: {
    initData() {
      this.newsList = []
      this.searchKeyword = ''
      this.currentFilter = 'all'
      this.currentPage = 1
    },

    // 日期格式化方法
    formatDate(dateString) {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(
        date.getDate()
      ).padStart(2, '0')}`
    },
    async fetchData() {
      try {
        this.$emit('loading', true)

        const response = await this.fetchNewsData()
        // 添加 status 到 category 的映射
        const statusToCategoryMap = {
          COMPLETED: 'completed',
          IMPORTANT: 'important',
          URGENT: 'urgent'
        }

        // 将接口返回的数据结构映射到组件使用的数据结构
        this.newsList = response.map((item) => ({
          ...item,
          category: statusToCategoryMap[item.status] || 'all'
        }))

        this.$emit('data-updated', {
          newsList: this.newsList,
          total: this.newsList.length,
          filtered: this.filteredNews.length
        })
      } catch (error) {
        console.error('Failed to fetch daily report data:', error)
        this.$emit('error', '获取线索日报数据失败，请重试')
      } finally {
        this.$emit('loading', false)
      }
    },

    async fetchNewsData() {
      try {
        // 请求接口
        // QUERY_CLUE_BY_PROGRESS_BY_ID
        let { data } = await this.$request({
          url: 'custom/clue/queryClueByProgress',
          method: 'post',
          params: this.dateRange || {},
          headers: {
            'Content-Type': 'application/json'
          }
        })
        return data
      } catch (error) {
        this.$emit('error', '获取线索日报数据失败，请重试')
      }
    },

    handleSearch() {
      // 防抖搜索
      clearTimeout(this.searchTimer)
      this.searchTimer = setTimeout(() => {
        this.performSearch()
      }, 500)
    },

    performSearch() {
      this.resetPagination()
      // 搜索逻辑已在 computed 中处理
    },

    switchFilter(filterValue) {
      this.currentFilter = filterValue
    },

    resetPagination() {
      this.currentPage = 1
    },

    async loadMore() {
      if (this.loadingMore || !this.hasMore) return

      this.loadingMore = true
      try {
        // 模拟加载延迟
        await new Promise((resolve) => setTimeout(resolve, 800))
        this.currentPage++
      } finally {
        this.loadingMore = false
      }
    },

    async refreshData() {
      await this.fetchData()
    },

    viewDetail(news) {
      // 查看详情逻辑
      console.log('View detail:', news)
      // 这里可以跳转到详情页面或打开模态框
    },

    shareNews(news) {
      // 分享逻辑
      console.log('Share news:', news)
      // 这里可以调用分享API或打开分享面板
    }
  }
}
</script>

<style scoped>
.daily-report {
  padding-bottom: 20px;
}

.search-bar {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  padding: 0 4px;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  outline: none;
}

.search-input:focus {
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.search-btn {
  padding: 8px 12px;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.filter-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  padding: 0 4px;
}

.filter-tab {
  padding: 6px 12px;
  background: #f5f5f5;
  border-radius: 16px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
}

.filter-tab.active {
  background: #4a90e2;
  color: white;
}

.filter-tab:hover:not(.active) {
  background: #e6e6e6;
}

.news-list {
  margin-bottom: 16px;
  overflow-y: auto;
  height: 65vh;
  scrollbar-width: thin;
  scrollbar-color: #cecece #f1f1f1;
}
/* Webkit 浏览器（Chrome、Safari）滚动条样式 */
.news-list::-webkit-scrollbar {
  width: 2px; /* 滚动条宽度：越小越细 */
}
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  margin-bottom: 16px;
  font-size: 14px;
}

.refresh-btn {
  padding: 8px 16px;
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.news-item {
  /* display: flex; */
  /* justify-content: space-between; */
  /* align-items: flex-start; */
  padding: 16px;

  background: linear-gradient(180deg, #f5f6f8 0%, #f8fafc 51%, #fbfcfd 100%);
  border-radius: 8px;
  margin-bottom: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s;
}

.news-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.news-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  /* margin-right: 12px; */
}

.news-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
}

.news-description {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 1.5em;
  line-height: 1.5em;
}

.news-meta {
  padding-top: 10px;
  display: flex;
  justify-content: space-between;
  /* flex-wrap: wrap; */
  gap: 8px;
  font-size: 11px;
  color: #999;
  margin-bottom: 8px;
}
.news-meta-item {
  display: flex;
  justify-content: center;
}
.news-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.tag {
  padding: 2px 6px;
  background: #f0f0f0;
  border-radius: 10px;
  font-size: 10px;
  color: #666;
}

.news-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-btn,
.share-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s;
}

.detail-btn {
  background: #4a90e2;
  color: white;
  min-width: 30px;
  text-align: center;
}

.detail-btn:hover {
  background: #357abd;
}

.share-btn {
  background: #f5f5f5;
  color: #666;
}

.share-btn:hover {
  background: #e6e6e6;
}

.load-more {
  text-align: center;
  margin-bottom: 16px;
}

.load-more-btn {
  padding: 10px 24px;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.load-more-btn:hover:not(:disabled) {
  background: #e6e6e6;
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.pagination-info {
  text-align: center;
  font-size: 12px;
  color: #999;
  padding: 8px;
}
</style>
