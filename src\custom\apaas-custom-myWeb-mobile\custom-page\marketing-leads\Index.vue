<template>
  <div class="app">
    <!-- Header -->
    <div class="header">
      <h1 class="title">
        线索列表
      </h1>
    </div>

    <!-- Date Range Selector with Tabs -->
    <DateSelector
      :date-range="dateRange"
      :current-filter="currentFilter"
      :active-tab="activeTab"
      @show-date-picker="showDatePicker"
      @show-filter-options="showFilterOptions"
      @apply-filters="applyFilters"
      @perform-search="performSearch"
      @switch-tab="switchTab"
    />

    <!-- Content Area -->
    <div class="content">
      <!-- Loading State -->
      <!-- <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div> -->

      <!-- Error State -->
      <!-- <div v-else-if="error" class="error-container">
        <p class="error-message">{{ error }}</p>
        <button class="retry-btn" @click="retryLoad">重试</button>
      </div> -->

      <!-- Tab Content -->
      <div>
        <keep-alive>
          <component
            :is="currentComponent"
            :key="componentKey"
            :date-range="dateRange"
            :filters="currentFilter"
            @loading="handleLoading"
            @error="handleError"
            @data-updated="handleDataUpdated"
          />
        </keep-alive>
      </div>
    </div>
  </div>
</template>

<script>
import LeadProgress from './components/LeadProgress.vue'
import DepartmentChart from './components/DepartmentChart.vue'
import DailyReport from './components/DailyReport.vue'
import DateSelector from './components/DateSelector.vue'

export default {
  name: 'ApaasCustomMarketingLeads',
  components: {
    LeadProgress,
    DepartmentChart,
    DailyReport,
    DateSelector
  },
  data() {
    return {
      activeTab: 'progress',
      loading: false,
      error: null,
      dateRange: {
        start: '2025/01/01',
        end: '2025/03/01'
      },
      currentFilter: {
        value: 'all',
        label: '线索类型'
      },
      componentKey: 0
    }
  },
  computed: {
    currentComponent() {
      const componentMap = {
        progress: 'LeadProgress',
        department: 'DepartmentChart',
        daily: 'DailyReport'
      }
      return componentMap[this.activeTab]
    }
  },
  async mounted() {
    await this.$store.dispatch('dictModule/fetchDictData')
  },
  methods: {
    switchTab(tab) {
      if (this.activeTab !== tab) {
        this.activeTab = tab
        this.error = null
        // 强制重新渲染组件以触发数据刷新
        this.componentKey++
      }
    },

    showDatePicker(type) {
      // 显示日期选择器的逻辑
      console.log(`Show date picker for ${type}`)
    },

    showFilterOptions() {
      // 显示筛选选项的逻辑
      console.log('Show filter options')
    },

    applyFilters(e) {
      // console.log('Apply filters', )
      this.dateRange = e
      // 应用筛选条件
      this.componentKey++
      console.log('Apply filters')
    },

    performSearch(e) {
      console.log('Perform search', e)
      // 执行搜索
      this.componentKey++
      console.log('执行搜索，Perform search')
    },

    handleLoading(isLoading) {
      this.loading = isLoading
    },

    handleError(errorMessage) {
      this.error = errorMessage
      this.loading = false
    },

    handleDataUpdated(data) {
      console.log('Data updated:', data)
      this.loading = false
      this.error = null
    },

    retryLoad() {
      this.error = null
      this.componentKey++
    }
  }
}
</script>

<style>
.app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  /* background: #f5f5f5; */
  min-height: 100vh;
  margin: 0 auto;
  background: linear-gradient(
    180deg,
    #1094d5 0%,
    rgba(16, 148, 213, 0.7397) 15%,
    rgba(220, 236, 248, 0.9879) 22%,
    #e6f0fa 39%,
    #e6f0fa 100%
  );
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: transparent;
  color: white;
  font-size: 14px;
  font-weight: 600;
}

.status-icons {
  display: flex;
  gap: 4px;
}

.header {
  text-align: center;
  padding: 12px 0;
  background: transparent;
}

.title {
  color: white;
  font-size: 18px;
  font-weight: 500;
}

.date-selector {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  margin: 0 16px;
  border-radius: 8px;
}

.date-input {
  display: flex;
  align-items: center;
  gap: 4px;
  color: white;
  font-size: 14px;
  cursor: pointer;
}

.to {
  color: white;
  font-size: 14px;
}

.arrow {
  font-size: 10px;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: white;
  font-size: 14px;
  cursor: pointer;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.filter-btn,
.search-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

.content {
  border-radius: 16px 16px 0 0;
  min-height: calc(100vh - 200px);
  padding: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.error-message {
  color: #ff4d4f;
  margin-bottom: 16px;
  text-align: center;
}

.retry-btn {
  background: #4a90e2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}
</style>
