<template>
  <div class="myWeb-mobile">
    <van-field clickable>
      <template #input>
        <input
          v-model="formValue"
          type="text"
          placeholder="请选择品系"
          :style="false ? 'color:#303133;width: 100%' : 'color:#969799;width: 100%'"
        />
      </template>
    </van-field>
    {{ formValue }}
    <van-popup v-model="cityShowP" position="bottom">
      <van-search
        v-model="cityValue"
        placeholder="请输入搜索关键词"
        input-align="center"
        @input="remoteMethod"
      ></van-search>
      <van-picker show-toolbar :loading="loading" :columns="options" @confirm="choose">
        <template #option="option">
          <div style="display: flex; flex-direction: column; align-items: center;">
            <div>{{ option.value }}</div>
          </div>
        </template>
      </van-picker>
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'ApaasCustomMyWebmobile',
  components: {},
  data: function() {
    return {
      formValue: '',
      cityShowP: false,
      cityValue: '',
      options: [],
      plate: '卡车',
      loading: false
    }
  },
  computed: {},
  created() {},
  methods: {
    test() {},
    choose(item) {
      let uuid = this.quesFormNameWidget('strain')
      for (var i in this.formData) {
        if (uuid === i) {
          this.formData[i] = item.value
        }
      }
    },
    getPlate() {
      let uuid = this.quesFormNameWidget('plate')
      console.log(uuid)
      if (this.formData[uuid] !== undefined) {
        console.log(this.formData[uuid])
        for (var i in this.formData) {
          console.log(this.formData, uuid, this.plateOptions)
          if (uuid === i) {
            this.plateOptions.forEach((v) => {
              if (this.formData[i][0] === v.key) {
                this.plate = v.value // 板块
              }
            })
          }
        }
      }
    },
    getSelectDataID(keyword, table) {
      this.$request({
        url:
          '/xdap-app/dataDictionary/query/dataDictionaryList?SECURITY_INFO=eyJhcHBJZCI6IjMwNjM4MzQ3MzY1NDg5MDQ5NiJ9',
        method: 'post',
        disableSuccessMsg: true, // 暂时必须，忽略消息影响
        params: { keyword: keyword, appId: '306383473654890496' }
      }).asyncThen((resp) => {
        if (resp.code === 'ok') {
          if (resp.table.length !== 0) {
            this.getSelectData(resp.table[0].id, table)
          }
        }
      })
    },
    getSelectData(id, table) {
      this.$request({
        url:
          '/xdap-app/dataDictionary/query/dictionaryValueList?SECURITY_INFO=eyJhcHBJZCI6IjMwNjM4MzQ3MzY1NDg5MDQ5NiJ9',
        method: 'post',
        disableSuccessMsg: true, // 暂时必须，忽略消息影响
        params: { page: 1, pageSize: 23, keyword: '', dictionaryId: id }
      }).asyncThen((resp) => {
        resp.table.forEach((v) => {
          let pool = { value: '', key: '' }
          pool.value = v.valueName
          pool.key = v.valueCode
          table.push(pool)
        })
      })
    },
    // 查询组件uuid
    quesFormNameWidget(value) {
      const widget = this.allTileFormItemList.find((item) => {
        if (item.modelField && item.modelField.split('.')[1] === value) {
          return true
        }
      })
      if (widget) {
        return widget.uuid
      }
      return null
    },
    // 公司简称查询
    remoteMethod() {
      // this.getPlate()
      if (this.plate !== '') {
        this.loading = true
        this.options = []
        this.$request({
          url:
            'https://apaas.app.yuchai.com/mpaas-api/gateway/ycloud-decision-making-service/wlxz/query',
          method: 'post',
          disableSuccessMsg: true, // 暂时必须，忽略消息影响
          params: {
            type: '1',
            zywbk: this.plate,
            strain: this.cityValue,
            zcpzxl: ''
          }
        }).asyncThen((resp) => {
          this.loading = false
          let data = resp.table
          data.forEach((element) => {
            let pool = { label: '', value: '' }
            pool.label = element
            pool.value = element
            this.options.push(pool)
          })
        })
      } else {
        this.$message({
          message: '请先选择板块！！！',
          type: 'warning'
        })
      }
    }
  }
}
</script>

<style lang="scss">
.myWeb-mobile {
  .van-field__control {
    display: block;
    box-sizing: border-box;
    width: 100%;
    min-width: 0;
    margin: 0;
    padding: 0;
    //  color: #0f0fce !important;
    line-height: inherit;
    text-align: left;
    background-color: transparent;
    border: 0;
    resize: none;
  }
  .van-field__control2 {
    display: block;
    box-sizing: border-box;
    width: 100%;
    min-width: 0;
    margin: 0;
    padding: 0;
    color: #010105;
    line-height: inherit;
    text-align: left;
    background-color: transparent;
    border: 0;
    resize: none;
  }
}
</style>
