<template>
  <div class="department-chart">
    <div class="chart-header">
      <h3>各市场部进度</h3>
      <div class="chart-legend">
        <span class="legend-item">
          <span class="legend-color blue"></span>
          今日
        </span>
        <span class="legend-item">
          <span class="legend-color orange"></span>
          本月累计
        </span>
        <span class="legend-item">
          <span class="legend-color gray"></span>
          达成率
        </span>
      </div>
    </div>

    <div class="chart-container">
      <div class="charts-box">
        <qiun-vue-ucharts
          type="bar"
          :opts="opts"
          :chartData="chartData"
          :canvasId="canvasId"
          background="rgba(0,0,0,1)"
          :dataLabel="false"
        />
      </div>
    </div>

    <!-- Chart Data Summary -->
    <div class="chart-summary">
      <div class="summary-item">
        <span class="summary-label">总部门数</span>
        <span class="summary-value">{{ chartData.series[0].data.length }}</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">平均达成率</span>
        <span class="summary-value">{{ averageRate }}%</span>
      </div>
      <div class="summary-item">
        <span class="summary-label">最高达成率</span>
        <span class="summary-value">{{ maxRate }}%</span>
      </div>
    </div>
  </div>
</template>

<script>
import qiunVueUcharts from '@qiun/vue-ucharts'

export default {

  name: 'DepartmentChart',
  components: {
    qiunVueUcharts
  },
  props: {
    dateRange: {
      type: Object,
      required: true
    },
    filters: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      canvasId: '',
      chartData: {
        categories: [],
        series: [
          { name: '抢单', data: [] },
          { name: '签单', data: [] },
          { name: '丢单', data: [] }
        ]
      },
      opts: {
        color: ['#1890FF', '#FF7F00', '#BAE0FF'],
        padding: [15, 30, 0, 5],
        enableScroll: false,
        legend: {
          show: true
        },
        xAxis: {
          boundaryGap: 'justify',
          disableGrid: false,
          min: 0,
          axisLine: false,
          max: 70
        },
        yAxis: {},
        extra: {
          bar: {
            type: 'stack',
            width: 30,
            activeBgColor: '#ffffff',
            activeBgOpacity: 0.18,
            categoryGap: 2
          }
        }
      }
    }
  },
  computed: {
    averageRate() {
      const rateData = this.chartData.series.find((item) => item.name === '达成率')?.data || []
      if (rateData.length === 0) return 0
      const total = rateData.reduce((sum, item) => sum + item, 0)
      return Math.round(total / rateData.length)
    },
    maxRate() {
      const rateData = this.chartData.series.find((item) => item.name === '达成率')?.data || []
      if (rateData.length === 0) return 0
      return Math.max(...rateData)
    }
  },
  watch: {
    dateRange: {
      handler() {
        this.fetchData()
      },
      deep: true
    },
    filters: {
      handler() {
        this.fetchData()
      },
      deep: true
    }
  },
  created() {
    this.canvasId = this.generateRandomString(32)
    this.initData()
  },
  mounted() {
    this.fetchData()
  },
  destroyed() {},
  methods: {
    generateRandomString(length) {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
      let result = ''
      for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      return result
    },
    initData() {
      this.chartData = {
        categories: [],
        series: [
          { name: '今日', data: [] },
          { name: '本月累计', data: [] },
          { name: '达成率', data: [] }
        ]
      }
    },
    async fetchData() {
      try {
        this.$emit('loading', true)
        const response = await this.fetchChartData()
        this.chartData = {
          categories: response.map((item) => item.name),
          series: [
            { name: '抢单', data: response.map((item) => item.today), textColor: '#FFFFFF' },
            { name: '签单', data: response.map((item) => item.monthly) },
            { name: '丢单', data: response.map((item) => item.rate) }
          ]
        }
        this.$emit('data-updated', {
          chartData: this.chartData,
          summary: {
            total: this.chartData.series[0].data.length,
            averageRate: this.averageRate,
            maxRate: this.maxRate
          }
        })
      } catch (error) {
        console.error('Failed to fetch department chart data:', error)
        this.$emit('error', '获取市场部图表数据失败，请重试')
      } finally {
        this.$emit('loading', false)
      }
    },
    async fetchChartData() {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve([
            { name: '市场部', today: 32, monthly: 45, rate: 71 },
            { name: '山东', today: 28, monthly: 38, rate: 74 },
            { name: '江西', today: 35, monthly: 42, rate: 83 },
            { name: '广西', today: 30, monthly: 40, rate: 75 },
            { name: '江苏', today: 25, monthly: 35, rate: 71 },
            { name: '安徽', today: 33, monthly: 44, rate: 75 },
            { name: '湖北', today: 29, monthly: 39, rate: 74 },
            { name: '河北', today: 31, monthly: 41, rate: 76 },
            { name: '湖南', today: 27, monthly: 37, rate: 73 },
            { name: '河南', today: 34, monthly: 43, rate: 79 },
            { name: '陕西', today: 26, monthly: 36, rate: 72 },
            { name: '甘肃', today: 32, monthly: 42, rate: 76 },
            { name: '宁夏', today: 28, monthly: 38, rate: 74 },
            { name: '天津', today: 30, monthly: 40, rate: 75 }
          ])
        }, 1200)
      })
    }
  }
}
</script>

<style scoped>
.department-chart {
  padding-bottom: 20px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.chart-header h3 {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.chart-legend {
  display: flex;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.legend-color {
  width: 8px;
  height: 8px;
  border-radius: 2px;
}

.legend-color.blue {
  background: #1890ff;
}
.legend-color.orange {
  background: #ff7f00;
}
.legend-color.gray {
  background: #d9d9d9;
}

.chart-container {
  width: 100%;
  height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
  overflow: hidden;
}

.charts-box {
  width: 100%;
  height: 100%;
}

.chart-summary {
  display: flex;
  justify-content: space-around;
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.summary-label {
  font-size: 12px;
  color: #666;
}

.summary-value {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}
</style>
