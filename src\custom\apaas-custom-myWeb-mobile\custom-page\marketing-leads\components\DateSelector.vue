<template>
  <div>
    <div style="padding: 0 20px;">
      <div class="date-selector">
        <div
          style="display: flex;flex:1;width: 100%;align-items: center;"
          @click="showFilterPopup = true"
        >
          <div class="date-input" @click="$emit('show-date-picker', 'start')">
            <span>{{ dateRange?.startDate || '请选择开始日期' }}</span>
            <span class="arrow">▼</span>
          </div>
          <div class="to">
            至
          </div>
          <div class="date-input" @click="$emit('show-date-picker', 'end')">
            <span>{{ dateRange?.endDate || '请选择结束日期' }}</span>
            <span class="arrow">▼</span>
          </div>
        </div>
        <div class="filter-bar">
          <div
            class="filter-item"
            @click="
              showFilterPopup = true
              $emit('show-filter-options')
            "
          >
            <span>{{ currentFilter.label }}</span>
            <span class="arrow">▼</span>
          </div>
          <div class="filter-actions">
            <div class="filter-btn" @click="showFilterPopup = true">
              <van-icon name="filter-o" />
              筛选
            </div>
            <div class="search-btn" @click="$emit('perform-search')">
              <van-icon name="search" />
              查询
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="tab-nav">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        :class="['tab-item', { active: activeTab === tab.value }]"
        @click="$emit('switch-tab', tab.value)"
      >
        {{ tab.label }}
      </div>
    </div>
    <!-- 新增的表单弹出层 -->
    <filter-popup
      v-model="showFilterPopup"
      @confirm="handleFilterSubmit"
      @reset="handleFilterReset"
      @close="handleColse"
    />
  </div>
</template>

<script>
// 导入新的弹出层组件
// import DateFilterPopup from '@/components/date-filter-popup.vue'
import FilterPopup from './FilterPopup.vue'

export default {
  name: 'DateSelector',
  components: {
    FilterPopup
    // DateFilterPopup // 注册新组件
  },
  props: {
    dateRange: {
      type: Object,
      required: true,
      default: () => ({
        start: '开始日期',
        end: '结束日期'
      })
    },
    currentFilter: {
      type: Object,
      required: true,
      default: () => ({
        value: 'all',
        label: '线索类型'
      })
    },
    activeTab: {
      type: String,
      default: 'progress'
    }
  },
  data() {
    return {
      tabs: [
        { label: '线索进度', value: 'progress' },
        { label: '按市场部', value: 'department' },
        { label: '线索日报', value: 'daily' }
      ],
      showFilterPopup: false // 修复初始值为 true 的问题
    }
  },
  methods: {
    // 处理表单提交
    handleFilterSubmit(formData) {
      // console.log('表单提交数据：', formData)
      this.$emit('apply-filters', formData)
      this.showFilterPopup = false
    },
    handleFilterReset(formData) {
      this.$emit('apply-filters', formData)
      this.showFilterPopup = false
    },
    handleColse() {
      this.showFilterPopup = false
    }
  }
}
</script>

<style scoped>
/* 保留必要的样式 */
.date-selector {
  display: flex;
  flex-direction: column;
  margin: 10px 0;
  background-color: rgba(255, 255, 255, 0.5);
  border: 1px solid white;
  border-radius: 10px;
}

.tab-nav {
  display: flex;
  gap: 40px;
  margin: 0 16px;
  border-radius: 8px;
}

.tab-item {
  text-align: center;
  padding: 12px 0;
  cursor: pointer;
}

.tab-item.active {
  font-weight: bold;
  position: relative;
  font-size: 18px;
  color: #1b7aff;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #1b7aff;
}

.date-input {
  flex: 1;
  color: #585858;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  background-color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.to {
  margin: 0 10px;
}

.arrow {
  margin-left: 8px;
  font-size: 12px;
}
.filter-bar {
  display: flex;
  color: #353535;
  margin-left: 10px;
  flex: 1;
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
}

/* 修复CSS语法错误 */
.filter-bar {
  cursor: pointer;
}

/* 弹出层样式补充 */
.filter-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  font-size: 16px;
  font-weight: bold;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.form-actions {
  display: flex;
  justify-content: space-around;
  padding: 15px;
  background-color: white;
  border-top: 1px solid #eee;
  position: fixed;
  bottom: 0;
  left: 20%;
  right: 0;
  z-index: 100;
}

/* 修复CSS语法错误 */
.filter-popup {
  padding: 15px;
  height: 100%;
  box-sizing: border-box;
}

/* 弹出层样式 */
.filter-popup {
  padding: 15px;
  height: 100%;
  box-sizing: border-box;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.popup-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 15px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 1px solid #eee;
}

.filter-item {
  display: flex;
  flex: 1;
  background-color: #ffffff;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  color: #353535;
  justify-content: center;
}

.filter-actions {
  margin-left: 10px;
}

.filter-btn,
.search-btn {
  background-color: #ffffff;
  margin-left: 5px;
  padding: 8px 16px;
  color: #1b7aff;
}
</style>
