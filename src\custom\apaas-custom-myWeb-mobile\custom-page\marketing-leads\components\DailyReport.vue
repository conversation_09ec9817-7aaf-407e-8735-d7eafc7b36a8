<template>
  <div class="lead-progress">
    <!-- Stats Cards -->
    <div class="stats-cards">
      <div class="stat-card blue">
        <div class="stat-icon">
          B
        </div>
        <div class="stat-content">
          <div class="stat-label">
            今日
          </div>
          <div class="stat-value">
            {{ statsData.today }}
          </div>
        </div>
      </div>
      <div class="stat-card purple">
        <div class="stat-icon">
          月
        </div>
        <div class="stat-content">
          <div class="stat-label">
            本月累计
          </div>
          <div class="stat-value">
            {{ statsData.monthly }}
          </div>
        </div>
      </div>
      <div class="stat-card orange">
        <div class="stat-icon">
          日
        </div>
        <div class="stat-content">
          <div class="stat-label">
            达成数计
          </div>
          <div class="stat-value">
            {{ statsData.achieved }}
          </div>
        </div>
      </div>
      <div class="stat-card light-blue">
        <div class="stat-icon">
          成
        </div>
        <div class="stat-content">
          <div class="stat-label">
            达成率
          </div>
          <div class="stat-value">
            {{ statsData.rate }}
          </div>
        </div>
      </div>
    </div>

    <!-- Data Table -->
    <div class="data-table">
      <div v-for="department in departmentData" :key="department.id" class="department-section">
        <div class="department-header" @click="toggleDepartment(department.id)">
          <div class="report-header">
            <h3 class="department-name">
              {{ formatDepartmentName(department.name) }}
            </h3>
          </div>
          <div class=" header-row">
            <div class="cell">
              <div>今日</div>
              <div>{{ department.data.reduce((acc, x) => acc + x.today, 0) }}</div>
            </div>
            <div class="cell">
              <div>本月累计</div>
              <div>{{ department.data.reduce((acc, x) => acc + x.monthly, 0) }}</div>
            </div>
            <div class="cell">
              <div>年累计</div>
              <div>{{ department.data.reduce((acc, x) => acc + x.achieved, 0) }}</div>
            </div>
            <div class="cell">
              <div>达成量</div>
              <div>{{ department.data.reduce((acc, x) => acc + x.rate, 0) }}</div>
            </div>
          </div>
          <span class="expand-icon" :class="{ expanded: department.expanded }">▼</span>
        </div>
        <div v-show="department.expanded">
          <div v-for="row in department.data" :key="row.id" class="data-row">
            <div class="cell type">
              {{ row.type }}
            </div>
            <div class="cell">
              {{ row.today }}
            </div>
            <div class="cell">
              {{ row.monthly }}
            </div>
            <div class="cell">
              {{ row.achieved }}
            </div>
            <div class="cell rate">
              {{ row.rate }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pull to Refresh -->
    <div v-if="refreshing" class="refresh-indicator">
      <div class="loading-spinner small"></div>
      <span>刷新中...</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LeadProgress',
  props: {
    dateRange: {
      type: Object,
      required: true
    },
    filters: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      statsData: {
        today: 0,
        monthly: 0,
        achieved: 0,
        rate: 0
      },
      departmentData: [],
      refreshing: false
    }
  },
  computed: {
    totalStats() {
      return this.departmentData.reduce(
        (acc, department) => {
          department.data.forEach((row) => {
            acc.today += Number(row.today) || 0
            acc.monthly += Number(row.monthly) || 0
            acc.achieved += Number(row.achieved) || 0
          })
          return acc
        },
        { today: 0, monthly: 0, achieved: 0 }
      )
    }
  },
  watch: {
    dateRange: {
      handler() {
        this.fetchData()
      },
      deep: true
    },
    filters: {
      handler() {
        this.fetchData()
      },
      deep: true
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    async initData() {
      // 初始化默认数据
      this.departmentData = [
        {
          id: 1,
          name: '广西市场部',
          expanded: true,
          data: []
        },
        {
          id: 2,
          name: '江西市场部',
          expanded: false,
          data: []
        },
        {
          id: 3,
          name: '安徽市场部',
          expanded: false,
          data: []
        },
        {
          id: 4,
          name: '山东市场部',
          expanded: false,
          data: []
        },
        {
          id: 5,
          name: '河南市场部',
          expanded: false,
          data: []
        },
        {
          id: 6,
          name: '山西市场部',
          expanded: false,
          data: []
        }
      ]
    },
    formatDepartmentName(name) {
      if (!name || name.length <= 2) return name
      // 仅在第一行第二个字符后换行
      return name.substring(0, 2) + '\n' + name.substring(2, name.length)
    },
    async fetchData() {
      try {
        this.$emit('loading', true)

        // 模拟API调用
        const [statsResponse, departmentResponse] = await Promise.all([
          this.fetchStatsData(),
          this.fetchDepartmentData()
        ])

        this.statsData = statsResponse
        this.updateDepartmentData(departmentResponse)

        this.$emit('data-updated', {
          stats: this.statsData,
          departments: this.departmentData
        })
      } catch (error) {
        console.error('Failed to fetch lead progress data:', error)
        this.$emit('error', '获取线索进度数据失败，请重试')
      } finally {
        this.$emit('loading', false)
      }
    },

    async fetchStatsData() {
      // 模拟API调用
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            today: 42,
            monthly: 156,
            achieved: 89,
            rate: 57
          })
        }, 1000)
      })
    },

    async fetchDepartmentData() {
      // 模拟API调用
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve([
            {
              departmentId: 1,
              data: [
                { id: 1, type: '数据', today: 12, monthly: 45, achieved: 23, rate: 51 },
                { id: 2, type: '套引', today: 8, monthly: 32, achieved: 18, rate: 56 },
                { id: 3, type: '回访', today: 15, monthly: 58, achieved: 31, rate: 53 },
                { id: 4, type: '专用', today: 7, monthly: 21, achieved: 17, rate: 81 }
              ]
            },
            {
              departmentId: 2,
              data: [{ id: 5, type: '数据', today: 10, monthly: 38, achieved: 20, rate: 53 }]
            },
            {
              departmentId: 3,
              data: [{ id: 6, type: '数据', today: 9, monthly: 35, achieved: 19, rate: 54 }]
            },
            {
              departmentId: 4,
              data: [{ id: 7, type: '数据', today: 11, monthly: 41, achieved: 22, rate: 54 }]
            },
            {
              departmentId: 5,
              data: [{ id: 8, type: '数据', today: 13, monthly: 47, achieved: 25, rate: 53 }]
            },
            {
              departmentId: 6,
              data: [{ id: 9, type: '数据', today: 8, monthly: 29, achieved: 16, rate: 55 }]
            }
          ])
        }, 800)
      })
    },

    updateDepartmentData(apiData) {
      apiData.forEach((item) => {
        const department = this.departmentData.find((d) => d.id === item.departmentId)
        if (department) {
          department.data = item.data
        }
      })
    },

    toggleDepartment(departmentId) {
      const department = this.departmentData.find((d) => d.id === departmentId)
      if (department) {
        department.expanded = !department.expanded
      }
    },

    async refreshData() {
      this.refreshing = true
      try {
        await this.fetchData()
      } finally {
        this.refreshing = false
      }
    }
  }
}
</script>

<style scoped>
.report-header {
  min-width: 66px;
  padding: 10px 10px;
  background: linear-gradient(90deg, #d1e4f9 0%, #e5f0fc 100%);
  line-height: 20px;
  font-size: 16px;
}

.department-name {
  font-weight: 800;
  word-break: break-word;
  white-space: pre-wrap;
}
.lead-progress {
  padding-bottom: 20px;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  margin-bottom: 16px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 12px 8px;
  text-align: center;
  display: flex;
  justify-content: space-around;
}

.stat-content {
  display: flex;
  flex-direction: column;
}
.stat-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 4px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.stat-card.blue .stat-icon {
  background: #1890ff;
}
.stat-card.purple .stat-icon {
  background: #722ed1;
}
.stat-card.orange .stat-icon {
  background: #fa8c16;
}
.stat-card.light-blue .stat-icon {
  background: #13c2c2;
}

.stat-label {
  font-size: 10px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.data-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.department-section {
  border-bottom: 1px solid #f0f0f0;
}

.department-section:last-child {
  border-bottom: none;
}

.department-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s;
}

.expand-icon {
  color: #999;
  font-size: 12px;
  transition: transform 0.2s;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.data-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  padding: 8px 16px;
  border-bottom: 1px solid #f5f5f5;
  font-size: 12px;
}

.data-row:last-child {
  border-bottom: none;
}

.header-row {
  background: #f8f9fa;
  font-weight: 500;
  color: #666;
  display: grid;
  font-size: 13px;
  grid-template-columns: repeat(4, 1fr);
}

.cell {
  padding: 4px;
  text-align: center;
  line-height: 20px;
}

.cell.type {
  text-align: left;
  color: #333;
}

.cell.rate {
  color: #1890ff;
}

.refresh-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  color: #666;
  font-size: 14px;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
