<template>
  <van-popup v-model="visible" position="bottom" :style="{ width: '100%' }">
    <div class="filter-popup">
      <div class="popup-header">
        <span>筛选条件</span>
        <van-icon name="cross" @click="close" />
      </div>
      <div class="filter-form">
        <van-cell-group>
          <van-cell title="日期范围">
            <div slot="default" class="date-range">
              <div class="date-input" @click="showStartDatePicker = true">
                <span>{{ form.startDate || '开始日期' }}</span>
                <span class="arrow">▼</span>
              </div>
              <span class="to">至</span>
              <div class="date-input" @click="showEndDatePicker = true">
                <span>{{ form.endDate || '结束日期' }}</span>
                <span class="arrow">▼</span>
              </div>
            </div>
          </van-cell>

          <van-cell title="意向动力">
            <div slot="default" class="selector" @click="showClueTypePicker = true">
              <span>{{ form.clueType || '请选择' }}</span>
              <span class="arrow">▼</span>
            </div>
          </van-cell>

          <van-cell title="地区">
            <div slot="default" class="selector" @click="showRegionPicker = true">
              <span>{{ form.region || '请选择' }}</span>
              <span class="arrow">▼</span>
            </div>
          </van-cell>

          <van-cell title="城市">
            <div slot="default" class="selector" @click="showCityPicker = true">
              <span>{{ form.city || '请选择' }}</span>
              <span class="arrow">▼</span>
            </div>
          </van-cell>

          <van-cell title="线索来源">
            <div slot="default" class="selector" @click="showClueFromPicker = true">
              <span>{{ form.clueFrom || '请选择' }}</span>
              <span class="arrow">▼</span>
            </div>
          </van-cell>

          <van-cell title="品牌">
            <div slot="default" class="selector" @click="showBrandPicker = true">
              <span>{{ form.brand || '请选择' }}</span>
              <span class="arrow">▼</span>
            </div>
          </van-cell>

          <van-cell title="燃料类型">
            <div slot="default" class="selector" @click="showFuelPicker = true">
              <span>{{ form.fuel || '请选择' }}</span>
              <span class="arrow">▼</span>
            </div>
          </van-cell>

          <van-cell title="需求数量">
            <van-field
              slot="default"
              v-model="form.needNum"
              placeholder="请输入需求数量"
              type="number"
            />
          </van-cell>

          <van-cell title="购车日期范围">
            <div slot="default" class="date-range">
              <div class="date-input" @click="showPurchaseDate1Picker = true">
                <span>{{ form.purchaseDate1 || '开始日期' }}</span>
                <span class="arrow">▼</span>
              </div>
              <span class="to">至</span>
              <div class="date-input" @click="showPurchaseDate2Picker = true">
                <span>{{ form.purchaseDate2 || '结束日期' }}</span>
                <span class="arrow">▼</span>
              </div>
            </div>
          </van-cell>
        </van-cell-group>

        <!-- 新增的日期选择器 -->
        <van-popup v-model="showStartDatePicker" position="bottom">
          <van-datetime-picker
            v-model="startDate"
            type="date"
            @confirm="handleDateConfirm('startDate')"
            @cancel="showStartDatePicker = false"
          />
        </van-popup>

        <van-popup v-model="showEndDatePicker" position="bottom">
          <van-datetime-picker
            v-model="endDate"
            type="date"
            @confirm="handleDateConfirm('endDate')"
            @cancel="showEndDatePicker = false"
          />
        </van-popup>

        <van-popup v-model="showPurchaseDate1Picker" position="bottom">
          <van-datetime-picker
            v-model="purchaseDate1"
            type="date"
            @confirm="handleDateConfirm('purchaseDate1')"
            @cancel="showPurchaseDate1Picker = false"
          />
        </van-popup>

        <van-popup v-model="showPurchaseDate2Picker" position="bottom">
          <van-datetime-picker
            v-model="purchaseDate2"
            type="date"
            @confirm="handleDateConfirm('purchaseDate2')"
            @cancel="showPurchaseDate2Picker = false"
          />
        </van-popup>

        <!-- 新增的选择器 -->
        <van-action-sheet
          v-model="showClueTypePicker"
          :actions="clueTypeOptions.map((option) => ({ label: option, value: option }))"
          @select="(value) => handleSelect('clueType', value)"
        />

        <van-action-sheet
          v-model="showRegionPicker"
          :actions="regionOptions.map((option) => ({ label: option, value: option }))"
          @select="(value) => handleSelect('region', value)"
        />

        <van-action-sheet
          v-model="showCityPicker"
          :actions="cityOptions.map((option) => ({ label: option, value: option }))"
          @select="(value) => handleSelect('city', value)"
        />

        <van-action-sheet
          v-model="showClueFromPicker"
          :actions="clueFromOptions.map((option) => ({ label: option, value: option }))"
          @select="(value) => handleSelect('clueFrom', value)"
        />

        <van-action-sheet
          v-model="showBrandPicker"
          :actions="brandOptions.map((option) => ({ label: option, value: option }))"
          @select="(value) => handleSelect('brand', value)"
        />

        <van-action-sheet
          v-model="showFuelPicker"
          :actions="fuelOptions.map((option) => ({ label: option, value: option }))"
          @select="(value) => handleSelect('fuel', value)"
        />
      </div>
      <div class="popup-actions">
        <button @click="reset">
          重置
        </button>
        <button @click="submit">
          提交
        </button>
      </div>
    </div>
  </van-popup>
</template>

<script>
export default {
  name: 'DateFilterPopup',
  props: {
    value: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      visible: this.value,
      showStartDatePicker: false,
      showEndDatePicker: false,
      showPurchaseDate1Picker: false,
      showPurchaseDate2Picker: false,
      showClueTypePicker: false,
      showRegionPicker: false,
      showCityPicker: false,
      showClueFromPicker: false,
      showBrandPicker: false,
      showFuelPicker: false,
      startDate: null,
      endDate: null,
      purchaseDate1: null,
      purchaseDate2: null,
      clueTypeOptions: ['玉柴', '竞品'],
      regionOptions: ['全部', '华北', '华东', '华南', '西北', '西南'],
      cityOptions: ['全部', '北京', '上海', '广州', '深圳', '成都'],
      clueFromOptions: ['全部', '官网', 'APP', '社交媒体', '线下活动'],
      brandOptions: ['全部', '玉柴', '东风', '解放', '重汽'],
      fuelOptions: ['全部', '柴油', '汽油', '天然气', '混合动力'],
      form: {
        startDate: '',
        endDate: '',
        clueType: '',
        region: '',
        city: '',
        clueFrom: '',
        brand: '',
        fuel: '',
        needNum: '',
        purchaseDate1: '',
        purchaseDate2: ''
      }
    }
  },
  watch: {
    value(newVal) {
      this.visible = newVal
    },
    visible(newVal) {
      this.$emit('input', newVal)
    }
  },
  methods: {
    close() {
      this.visible = false
    },
    reset() {
      // 重置表单逻辑
      this.form = {
        startDate: '',
        endDate: '',
        clueType: '',
        region: '',
        city: '',
        clueFrom: '',
        brand: '',
        fuel: '',
        needNum: '',
        purchaseDate1: '',
        purchaseDate2: ''
      }
    },
    submit() {
      // 提交表单逻辑
      this.$emit('submit', this.form)
      this.close()
    },
    handleDateConfirm(field) {
      const date = this[field]
      this.form[field] = this.formatDate(date)

      // 关闭对应的日期选择器
      if (field === 'startDate') this.showStartDatePicker = false
      if (field === 'endDate') this.showEndDatePicker = false
      if (field === 'purchaseDate1') this.showPurchaseDate1Picker = false
      if (field === 'purchaseDate2') this.showPurchaseDate2Picker = false
    },
    handleSelect(field, value) {
      this.form[field] = value

      // 关闭对应的选择器
      if (field === 'clueType') this.showClueTypePicker = false
      if (field === 'region') this.showRegionPicker = false
      if (field === 'city') this.showCityPicker = false
      if (field === 'clueFrom') this.showClueFromPicker = false
      if (field === 'brand') this.showBrandPicker = false
      if (field === 'fuel') this.showFuelPicker = false
    },
    formatDate(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    }
  }
}
</script>

<style scoped>
.van-popup--bottom {
  width: 100% !important;
  max-width: 100%;
  left: 0;
  right: 0;
  padding: 0;
  margin: 0;
}

.filter-popup {
  padding: 15px;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  overflow-y: auto;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.popup-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 15px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 1px solid #eee;
  z-index: 100;
}

.popup-actions button {
  flex: 1;
  margin: 0 10px;
  padding: 12px 0;
  border-radius: 4px;
  border: 1px solid #1b7aff;
  background-color: #1b7aff;
  color: white;
  font-weight: bold;
}

.filter-form {
  overflow-y: auto;
  max-height: calc(100vh - 220px);
  padding-bottom: 70px; /* 为底部按钮留出空间 */
}

.date-range {
  display: flex;
  align-items: center;
}

.date-input {
  flex: 1;
  color: #585858;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  background-color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.to {
  margin: 0 10px;
  color: #666;
}

.selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #ffffff;
  cursor: pointer;
}

.arrow {
  font-size: 12px;
}

.van-cell__title {
  max-width: 110px;
  flex: 1;
}

.van-cell__value {
  flex: 2;
}

.date-input span,
.selector span {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>
